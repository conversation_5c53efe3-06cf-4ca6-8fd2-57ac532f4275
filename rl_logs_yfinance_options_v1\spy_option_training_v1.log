2025-09-07 14:23:48,168 - INFO - SPY.py:2359 - Logging configured for Option Trader v1.
2025-09-07 14:23:48,168 - INFO - SPY.py:2360 - Log file: C:\Users\<USER>\Desktop\OHLC of SPY + close_IRX+ close_TNX\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-07 14:23:48,168 - INFO - SPY.py:2361 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-07 14:23:48,168 - INFO - SPY.py:2369 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-07 14:23:48,171 - INFO - SPY.py:5853 - --- Option Trader v3.1 ---
2025-09-07 14:23:48,172 - INFO - SPY.py:5863 - Signal generation mode...
2025-09-07 14:23:52,239 - INFO - SPY.py:2508 - Using min_periods_override=50 for SPY
2025-09-07 14:23:52,244 - INFO - SPY.py:2651 - SPY expanded feature set: Keeping OHLC columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-07 14:23:52,245 - INFO - SPY.py:2657 - Using expanded OHLC feature set for SPY
2025-09-07 14:23:52,245 - INFO - SPY.py:2660 - SPY: Keeping OHLC columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-07 14:23:52,248 - INFO - SPY.py:2688 - Processed SPY data shape: (63, 4). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-07 14:23:52,249 - INFO - SPY.py:2689 - Data quality check: 0/252 (0.0%) zero values
2025-09-07 14:23:52,249 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1757226228 SUCCESS in 4.08s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-07 14:23:53,051 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-07 14:23:53,434 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^VIX
2025-09-07 14:23:53,435 - INFO - SPY.py:2532 - Processing ^VIX data with shape (63, 7)
2025-09-07 14:23:53,435 - INFO - SPY.py:2533 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-07 14:23:53,435 - INFO - SPY.py:2535 - Raw ^VIX Close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-07 14:23:53,438 - INFO - SPY.py:2455 - Applying VIX validation for VIX
2025-09-07 14:23:53,439 - INFO - SPY.py:2614 - Processed VIX close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-07 14:23:53,440 - INFO - SPY.py:2647 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-07 14:23:53,442 - INFO - SPY.py:2688 - Processed ^VIX data shape: (63, 1). Columns: ['close_VIX']
2025-09-07 14:23:53,442 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-07 14:23:53,442 - INFO - SPY.py:1161 - VALIDATION: ^VIX last value: 20.81999969482422
2025-09-07 14:23:53,442 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1757226232 SUCCESS in 1.19s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-07 14:23:54,245 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 25s
2025-09-07 14:23:54,687 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^VIX3M
2025-09-07 14:23:54,690 - INFO - SPY.py:2647 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-07 14:23:54,691 - INFO - SPY.py:2688 - Processed ^VIX3M data shape: (63, 1). Columns: ['close_VIX3M']
2025-09-07 14:23:54,692 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-07 14:23:54,692 - INFO - SPY.py:1161 - VALIDATION: ^VIX3M last value: 22.6200008392334
2025-09-07 14:23:54,693 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1757226233 SUCCESS in 1.25s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-07 14:23:55,495 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-07 14:23:55,819 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^IRX
2025-09-07 14:23:55,819 - INFO - SPY.py:2532 - Processing ^IRX data with shape (63, 7)
2025-09-07 14:23:55,819 - INFO - SPY.py:2533 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-07 14:23:55,820 - INFO - SPY.py:2535 - Raw ^IRX Close values - first: 4.188000202178955, last: 4.239999771118164
2025-09-07 14:23:55,822 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-09-07 14:23:55,822 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-07 14:23:55,823 - INFO - SPY.py:2482 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-09-07 14:23:55,824 - INFO - SPY.py:2485 - IRX (3-month Treasury yield) after conversion to decimal: min=0.041500, max=0.042850, median=0.042080
2025-09-07 14:23:55,825 - INFO - SPY.py:2614 - Processed IRX (3-month Treasury yield) close values - first: 0.04188000202178955, last: 0.04239999771118164
2025-09-07 14:23:55,825 - INFO - SPY.py:2621 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.042400 (4.2400%)
2025-09-07 14:23:55,827 - INFO - SPY.py:2647 - Market index ^IRX: Keeping only ['close_IRX']
2025-09-07 14:23:55,828 - INFO - SPY.py:2688 - Processed ^IRX data shape: (63, 1). Columns: ['close_IRX']
2025-09-07 14:23:55,829 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-07 14:23:55,829 - INFO - SPY.py:1161 - VALIDATION: ^IRX last value: 0.04239999771118164
2025-09-07 14:23:55,829 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1757226234 SUCCESS in 1.14s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-09-07 14:23:56,632 - INFO - SPY.py:2187 - Attempt 1/3 with timeout 20s
2025-09-07 14:23:56,990 - INFO - SPY.py:2508 - Using min_periods_override=50 for ^TNX
2025-09-07 14:23:56,993 - INFO - SPY.py:2472 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-07 14:23:56,993 - INFO - SPY.py:2473 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-07 14:23:56,994 - INFO - SPY.py:2482 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-07 14:23:56,995 - INFO - SPY.py:2485 - TNX (10-year Treasury yield) after conversion to decimal: min=0.039850, max=0.045960, median=0.043650
2025-09-07 14:23:56,995 - INFO - SPY.py:2614 - Processed TNX (10-year Treasury yield) close values - first: 0.04306000232696533, last: 0.04423999786376953
2025-09-07 14:23:56,996 - INFO - SPY.py:2621 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.044240 (4.4240%)
2025-09-07 14:23:56,997 - INFO - SPY.py:2647 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-07 14:23:56,998 - INFO - SPY.py:2688 - Processed ^TNX data shape: (63, 1). Columns: ['close_TNX']
2025-09-07 14:23:56,999 - INFO - SPY.py:2689 - Data quality check: 0/63 (0.0%) zero values
2025-09-07 14:23:57,000 - INFO - SPY.py:1161 - VALIDATION: ^TNX last value: 0.04423999786376953
2025-09-07 14:23:57,000 - INFO - SPY.py:734 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1757226235 SUCCESS in 1.17s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-07 14:23:57,007 - INFO - SPY.py:2866 - Created master index with 63 unique dates from 2025-03-17 to 2025-06-13
2025-09-07 14:23:57,007 - INFO - SPY.py:2871 - Processing ticker SPY for reindexing: shape=(63, 4), columns=['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY']
2025-09-07 14:23:57,009 - INFO - SPY.py:2877 - About to reindex SPY with master_index length 63
2025-09-07 14:23:57,010 - INFO - SPY.py:2749 - Index overlap for SPY: 100.0% (63/63 dates)
2025-09-07 14:23:57,014 - INFO - SPY.py:2812 - Successfully reindexed SPY with validation passed
2025-09-07 14:23:57,015 - INFO - SPY.py:2881 - Successfully reindexed SPY to master index. New shape: (63, 4)
2025-09-07 14:23:57,016 - INFO - SPY.py:2871 - Processing ticker ^VIX for reindexing: shape=(63, 1), columns=['close_VIX']
2025-09-07 14:23:57,018 - INFO - SPY.py:2877 - About to reindex ^VIX with master_index length 63
2025-09-07 14:23:57,018 - INFO - SPY.py:2749 - Index overlap for ^VIX: 100.0% (63/63 dates)
2025-09-07 14:23:57,020 - INFO - SPY.py:2812 - Successfully reindexed ^VIX with validation passed
2025-09-07 14:23:57,020 - INFO - SPY.py:2881 - Successfully reindexed ^VIX to master index. New shape: (63, 1)
2025-09-07 14:23:57,021 - INFO - SPY.py:2871 - Processing ticker ^VIX3M for reindexing: shape=(63, 1), columns=['close_VIX3M']
2025-09-07 14:23:57,022 - INFO - SPY.py:2877 - About to reindex ^VIX3M with master_index length 63
2025-09-07 14:23:57,023 - INFO - SPY.py:2749 - Index overlap for ^VIX3M: 100.0% (63/63 dates)
2025-09-07 14:23:57,025 - INFO - SPY.py:2812 - Successfully reindexed ^VIX3M with validation passed
2025-09-07 14:23:57,025 - INFO - SPY.py:2881 - Successfully reindexed ^VIX3M to master index. New shape: (63, 1)
2025-09-07 14:23:57,025 - INFO - SPY.py:2871 - Processing ticker ^IRX for reindexing: shape=(63, 1), columns=['close_IRX']
2025-09-07 14:23:57,026 - INFO - SPY.py:2877 - About to reindex ^IRX with master_index length 63
2025-09-07 14:23:57,026 - INFO - SPY.py:2749 - Index overlap for ^IRX: 100.0% (63/63 dates)
2025-09-07 14:23:57,027 - INFO - SPY.py:2812 - Successfully reindexed ^IRX with validation passed
2025-09-07 14:23:57,028 - INFO - SPY.py:2881 - Successfully reindexed ^IRX to master index. New shape: (63, 1)
2025-09-07 14:23:57,028 - INFO - SPY.py:2871 - Processing ticker ^TNX for reindexing: shape=(63, 1), columns=['close_TNX']
2025-09-07 14:23:57,029 - INFO - SPY.py:2877 - About to reindex ^TNX with master_index length 63
2025-09-07 14:23:57,030 - INFO - SPY.py:2749 - Index overlap for ^TNX: 100.0% (63/63 dates)
2025-09-07 14:23:57,031 - INFO - SPY.py:2812 - Successfully reindexed ^TNX with validation passed
2025-09-07 14:23:57,031 - INFO - SPY.py:2881 - Successfully reindexed ^TNX to master index. New shape: (63, 1)
2025-09-07 14:23:57,031 - INFO - SPY.py:2899 - Starting combine features with SPY shape: (63, 4)
2025-09-07 14:23:57,031 - INFO - SPY.py:2900 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-07 14:23:57,032 - INFO - SPY.py:2916 - Merging ^VIX (Shape: (63, 1), Columns: ['close_VIX'])
2025-09-07 14:23:57,032 - INFO - SPY.py:2926 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-09-07 14:23:57,033 - INFO - SPY.py:2962 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,033 - INFO - SPY.py:2963 - ^VIX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,035 - INFO - SPY.py:2980 - ^VIX close_VIX sample after join (first 3): [20.510000228881836, 21.700000762939453, 19.899999618530273], last: 20.81999969482422
2025-09-07 14:23:57,035 - INFO - SPY.py:2998 - After joining ^VIX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX']
2025-09-07 14:23:57,035 - INFO - SPY.py:2916 - Merging ^VIX3M (Shape: (63, 1), Columns: ['close_VIX3M'])
2025-09-07 14:23:57,036 - INFO - SPY.py:2926 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-09-07 14:23:57,038 - INFO - SPY.py:2962 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,039 - INFO - SPY.py:2963 - ^VIX3M index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,040 - INFO - SPY.py:2980 - ^VIX3M close_VIX3M sample after join (first 3): [21.299999237060547, 21.969999313354492, 20.829999923706055], last: 22.6200008392334
2025-09-07 14:23:57,041 - INFO - SPY.py:2998 - After joining ^VIX3M, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M']
2025-09-07 14:23:57,041 - INFO - SPY.py:2916 - Merging ^IRX (Shape: (63, 1), Columns: ['close_IRX'])
2025-09-07 14:23:57,041 - INFO - SPY.py:2926 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-09-07 14:23:57,043 - INFO - SPY.py:2962 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,043 - INFO - SPY.py:2963 - ^IRX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,045 - INFO - SPY.py:2980 - ^IRX close_IRX sample after join (first 3): [0.04188000202178955, 0.04195000171661377, 0.04190000057220459], last: 0.04239999771118164
2025-09-07 14:23:57,045 - INFO - SPY.py:2998 - After joining ^IRX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-09-07 14:23:57,046 - INFO - SPY.py:2916 - Merging ^TNX (Shape: (63, 1), Columns: ['close_TNX'])
2025-09-07 14:23:57,046 - INFO - SPY.py:2926 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-09-07 14:23:57,048 - INFO - SPY.py:2962 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,048 - INFO - SPY.py:2963 - ^TNX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-07 14:23:57,049 - INFO - SPY.py:2980 - ^TNX close_TNX sample after join (first 3): [0.04306000232696533, 0.042810001373291016, 0.042560000419616696], last: 0.04423999786376953
2025-09-07 14:23:57,050 - INFO - SPY.py:2998 - After joining ^TNX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-07 14:23:57,050 - INFO - SPY.py:3009 - Shape after merging all tickers: (63, 8)
2025-09-07 14:23:57,052 - INFO - SPY.py:3192 - Skipping VIX derived feature calculations - using reduced feature set
2025-09-07 14:23:57,052 - INFO - SPY.py:3196 - Skipping Treasury derived feature calculations - using reduced feature set
2025-09-07 14:23:57,052 - INFO - SPY.py:3095 - Volume column volume_SPY not found, skipping volume normalization
2025-09-07 14:23:57,053 - INFO - SPY.py:3098 - Performing final validation and cleanup...
2025-09-07 14:23:57,054 - INFO - SPY.py:3113 - Created expanded feature set with shape: (63, 6)
2025-09-07 14:23:57,054 - INFO - SPY.py:3114 - Expanded feature columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_TNX', 'close_IRX']
2025-09-07 14:23:57,054 - INFO - SPY.py:3119 - Stored full market data for option pricing with shape: (63, 8)
2025-09-07 14:23:57,054 - INFO - SPY.py:3120 - Full market data columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-07 14:23:57,054 - INFO - SPY.py:3124 - Expected columns for expanded feature set: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_TNX', 'close_IRX']
2025-09-07 14:23:57,055 - INFO - SPY.py:3150 - TNX data validation passed: min=0.039850, max=0.045960, mean=0.043524
2025-09-07 14:23:57,055 - INFO - SPY.py:3167 - IRX data validation passed: min=0.041500, max=0.042850, mean=0.042136
2025-09-07 14:23:57,055 - INFO - SPY.py:3214 - Skipping feature distribution validation - using expanded feature set with SPY OHLC + TNX + IRX
2025-09-07 14:23:57,056 - INFO - SPY.py:3176 - Final expanded features shape: (63, 6) (6 features: SPY OHLC + TNX + IRX)
2025-09-07 14:23:57,056 - INFO - SPY.py:3177 - Final expanded columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'close_TNX', 'close_IRX']
2025-09-07 14:23:57,056 - INFO - SPY.py:5915 - N_MARKET_FEATURES: 6
2025-09-07 14:23:57,056 - ERROR - SPY.py:5944 - Model not found: C:\Users\<USER>\Desktop\OHLC of SPY + close_IRX+ close_TNX\models_options_v1\ppo_spy_option_trader_v1.zip
2025-09-07 14:23:57,058 - INFO - SPY.py:9289 - [INFO] Main: Script execution completed
2025-09-07 14:23:57,058 - INFO - SPY.py:779 - [INFO] PerformanceMonitor: Performance Summary: 5/5 operations successful (100.0% success rate)
2025-09-07 14:23:57,058 - INFO - SPY.py:787 - [INFO] PerformanceMonitor: Average refactored_fetch time: 1.76s
